import PropTypes from "prop-types";
import FormInput from "../../../components/form/FormInput";

const RoleSection = ({ control }) => {
  return (
    <>
      <p className="font-medium mt-2">نقش</p>

      <div className="flex items-end md:flex-nowrap flex-wrap gap-3">
        <FormInput
          control={control}
          name="rolePersianName"
          inputProps={{
            placeholder: "عنوان نقش",
            radius: "full",
            size: "lg",
            classNames: {
              base: "md:max-w-xs",
              inputWrapper:
                "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
              input: "text-sm",
            },
          }}
        />
        <FormInput
          control={control}
          name="roleLatinName"
          inputProps={{
            placeholder: "نام لاتین",
            radius: "full",
            size: "lg",
            classNames: {
              base: "md:max-w-xs",
              inputWrapper:
                "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
              input: "text-sm",
            },
          }}
        />
      </div>
    </>
  );
};

RoleSection.propTypes = {
  control: PropTypes.object.isRequired,
};

export default RoleSection;
